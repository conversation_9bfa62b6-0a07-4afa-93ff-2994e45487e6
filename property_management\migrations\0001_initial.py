# Generated by Django 5.2.3 on 2025-06-18 09:08

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PropertyFeePayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('building_number', models.CharField(max_length=10, verbose_name='楼号')),
                ('unit_number', models.CharField(max_length=10, verbose_name='单元号')),
                ('room_number', models.CharField(max_length=10, verbose_name='房号')),
                ('area', models.DecimalField(decimal_places=2, max_digits=7, verbose_name='平米数')),
                ('owner_name', models.CharField(max_length=50, verbose_name='业主姓名')),
                ('has_basement', models.BooleanField(default=False, verbose_name='地下室')),
                ('has_parking', models.BooleanField(default=False, verbose_name='车位')),
                ('payment_date', models.DateField(verbose_name='交费时间')),
                ('start_date', models.DateField(verbose_name='物业费开始时间')),
                ('end_date', models.DateField(verbose_name='物业费结束时间')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='金额')),
                ('months', models.IntegerField(default=1, verbose_name='缴费月数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '商品房物业费',
                'verbose_name_plural': '商品房物业费流水统计',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='Property',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('building_number', models.CharField(max_length=10, verbose_name='楼号')),
                ('unit_number', models.CharField(max_length=10, verbose_name='单元号')),
                ('room_number', models.CharField(max_length=10, verbose_name='房号')),
                ('area', models.DecimalField(decimal_places=2, max_digits=7, verbose_name='平米数')),
                ('owner_name', models.CharField(max_length=50, verbose_name='业主姓名')),
                ('has_basement', models.BooleanField(default=False, verbose_name='地下室')),
                ('has_parking', models.BooleanField(default=False, verbose_name='车位')),
                ('owner_phone', models.CharField(max_length=20, verbose_name='业主电话')),
                ('property_fee', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='物业费总额')),
                ('fee_end_date', models.DateField(verbose_name='物业费到期日期')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '商品房',
                'verbose_name_plural': '商品房物业费列表',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['building_number', 'unit_number', 'room_number'], name='property_ma_buildin_25e2ef_idx'), models.Index(fields=['owner_name'], name='property_ma_owner_n_44f081_idx'), models.Index(fields=['fee_end_date'], name='property_ma_fee_end_60dc46_idx')],
            },
        ),
    ]
