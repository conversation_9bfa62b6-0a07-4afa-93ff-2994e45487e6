# Generated by Django 5.2.3 on 2025-06-18 09:08

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ParkingCheckout',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('building_number', models.CharField(max_length=10, verbose_name='楼号')),
                ('room_number', models.CharField(max_length=10, verbose_name='房号')),
                ('tenant_name', models.CharField(max_length=50, verbose_name='姓名')),
                ('phone', models.Char<PERSON>ield(max_length=20, verbose_name='电话')),
                ('parking_number', models.CharField(max_length=20, verbose_name='车位号')),
                ('license_plate', models.CharField(blank=True, max_length=20, null=True, verbose_name='车牌号')),
                ('checkout_date', models.DateField(verbose_name='退租日期')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '车位退租',
                'verbose_name_plural': '退车位列表',
                'ordering': ['-checkout_date'],
            },
        ),
        migrations.CreateModel(
            name='ParkingFeePayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('building_number', models.CharField(max_length=10, verbose_name='楼号')),
                ('room_number', models.CharField(max_length=10, verbose_name='房号')),
                ('tenant_name', models.CharField(max_length=50, verbose_name='姓名')),
                ('phone', models.CharField(max_length=20, verbose_name='电话')),
                ('parking_number', models.CharField(max_length=20, verbose_name='车位号')),
                ('license_plate', models.CharField(blank=True, max_length=20, null=True, verbose_name='车牌号')),
                ('payment_date', models.DateField(verbose_name='交费时间')),
                ('start_date', models.DateField(verbose_name='物业费开始时间')),
                ('end_date', models.DateField(verbose_name='物业费结束时间')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='金额')),
                ('months', models.IntegerField(default=1, verbose_name='缴费月数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '车位物业费',
                'verbose_name_plural': '车位物业费流水',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='ParkingSpace',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('building_number', models.CharField(max_length=10, verbose_name='楼号')),
                ('room_number', models.CharField(max_length=10, verbose_name='房号')),
                ('tenant_name', models.CharField(max_length=50, verbose_name='姓名')),
                ('phone', models.CharField(max_length=20, verbose_name='电话')),
                ('parking_number', models.CharField(max_length=20, verbose_name='车位号')),
                ('license_plate', models.CharField(max_length=20, verbose_name='车牌号')),
                ('rent_start_date', models.DateField(verbose_name='租车位时间')),
                ('fee_end_date', models.DateField(verbose_name='物业费到期时间')),
                ('property_fee', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='物业费')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否在租')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '车位',
                'verbose_name_plural': '地下车位出租列表',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['building_number', 'room_number'], name='parking_man_buildin_99b033_idx'), models.Index(fields=['tenant_name'], name='parking_man_tenant__f22b09_idx'), models.Index(fields=['parking_number'], name='parking_man_parking_153852_idx'), models.Index(fields=['license_plate'], name='parking_man_license_68b37e_idx'), models.Index(fields=['fee_end_date'], name='parking_man_fee_end_19147d_idx')],
            },
        ),
    ]
