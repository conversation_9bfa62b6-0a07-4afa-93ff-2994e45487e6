from django.contrib import admin
from .models import Shop, ShopCheckout, ShopFeePayment


@admin.register(Shop)
class ShopAdmin(admin.ModelAdmin):
    list_display = ('shop_number', 'tenant_name', 'phone', 'area', 
                    'lease_start_date', 'fee_end_date', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('shop_number', 'tenant_name', 'phone', 'id_card')
    list_per_page = 20


@admin.register(ShopCheckout)
class ShopCheckoutAdmin(admin.ModelAdmin):
    list_display = ('shop_number', 'tenant_name', 'phone', 'checkout_date')
    list_filter = ('checkout_date',)
    search_fields = ('shop_number', 'tenant_name', 'phone')
    list_per_page = 20


@admin.register(ShopFeePayment)
class ShopFeePaymentAdmin(admin.ModelAdmin):
    list_display = ('shop_number', 'tenant_name', 'payment_date', 
                    'start_date', 'end_date', 'amount')
    list_filter = ('payment_date',)
    search_fields = ('shop_number', 'tenant_name', 'phone')
    list_per_page = 20 