from rest_framework import serializers
from .models import ParkingSpace, ParkingCheckout, ParkingFeePayment


class ParkingSpaceSerializer(serializers.ModelSerializer):
    remaining_days = serializers.SerializerMethodField()
    
    class Meta:
        model = ParkingSpace
        fields = '__all__'
    
    def get_remaining_days(self, obj):
        return obj.calculate_remaining_days()


class ParkingListSerializer(serializers.ModelSerializer):
    remaining_days = serializers.SerializerMethodField()
    
    class Meta:
        model = ParkingSpace
        fields = [
            'id', 'building_number', 'room_number', 'tenant_name', 'phone',
            'parking_number', 'license_plate', 'rent_start_date',
            'fee_end_date', 'remaining_days', 'is_active'
        ]
    
    def get_remaining_days(self, obj):
        return obj.calculate_remaining_days()


class ParkingExpiredSerializer(serializers.ModelSerializer):
    overdue_days = serializers.SerializerMethodField()
    
    class Meta:
        model = ParkingSpace
        fields = [
            'id', 'building_number', 'room_number', 'tenant_name', 'phone',
            'parking_number', 'license_plate', 'fee_end_date', 'overdue_days'
        ]
    
    def get_overdue_days(self, obj):
        return obj.calculate_overdue_days()


class ParkingCheckoutSerializer(serializers.ModelSerializer):
    class Meta:
        model = ParkingCheckout
        fields = '__all__'


class ParkingFeePaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = ParkingFeePayment
        fields = '__all__'


class RenewFeeSerializer(serializers.Serializer):
    parking_id = serializers.IntegerField()
    months = serializers.IntegerField(min_value=1) 