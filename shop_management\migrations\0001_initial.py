# Generated by Django 5.2.3 on 2025-06-18 09:08

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ShopCheckout',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('shop_number', models.CharField(max_length=20, verbose_name='商铺号')),
                ('tenant_name', models.CharField(max_length=50, verbose_name='租户姓名')),
                ('phone', models.CharField(max_length=20, verbose_name='电话')),
                ('area', models.DecimalField(decimal_places=2, max_digits=7, verbose_name='平米数')),
                ('checkout_date', models.DateField(verbose_name='退租日期')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '商铺退租',
                'verbose_name_plural': '商铺退房列表',
                'ordering': ['-checkout_date'],
            },
        ),
        migrations.CreateModel(
            name='ShopFeePayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('shop_number', models.CharField(max_length=20, verbose_name='商铺号')),
                ('tenant_name', models.CharField(max_length=50, verbose_name='租户姓名')),
                ('phone', models.CharField(max_length=20, verbose_name='电话')),
                ('area', models.DecimalField(decimal_places=2, max_digits=7, verbose_name='平米数')),
                ('payment_date', models.DateField(verbose_name='交费时间')),
                ('start_date', models.DateField(verbose_name='物业费开始时间')),
                ('end_date', models.DateField(verbose_name='物业费结束时间')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='金额')),
                ('months', models.IntegerField(default=1, verbose_name='缴费月数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '商铺物业费',
                'verbose_name_plural': '商铺物业费流水',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='Shop',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('shop_number', models.CharField(max_length=20, verbose_name='商铺号')),
                ('tenant_name', models.CharField(max_length=50, verbose_name='租户姓名')),
                ('phone', models.CharField(max_length=20, verbose_name='电话')),
                ('id_card', models.CharField(max_length=18, verbose_name='身份证号')),
                ('area', models.DecimalField(decimal_places=2, max_digits=7, verbose_name='平米数')),
                ('lease_start_date', models.DateField(verbose_name='起租时间')),
                ('fee_end_date', models.DateField(verbose_name='物业费到期时间')),
                ('property_fee', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='物业费')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否在租')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '商铺',
                'verbose_name_plural': '商铺租房管理',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['shop_number'], name='shop_manage_shop_nu_3d5276_idx'), models.Index(fields=['tenant_name'], name='shop_manage_tenant__0a25f4_idx'), models.Index(fields=['fee_end_date'], name='shop_manage_fee_end_6b97d6_idx')],
            },
        ),
    ]
