from django.db import models
from django.utils import timezone
from datetime import timedelta


class Shop(models.Model):
    """商铺模型"""
    shop_number = models.CharField(max_length=20, verbose_name="商铺号")
    tenant_name = models.CharField(max_length=50, verbose_name="租户姓名")
    phone = models.CharField(max_length=20, verbose_name="电话")
    id_card = models.CharField(max_length=18, verbose_name="身份证号")
    area = models.DecimalField(max_digits=7, decimal_places=2, verbose_name="平米数")
    lease_start_date = models.DateField(verbose_name="起租时间")
    fee_end_date = models.DateField(verbose_name="物业费到期时间")
    property_fee = models.DecimalField(max_digits=8, decimal_places=2, verbose_name="物业费")
    is_active = models.BooleanField(default=True, verbose_name="是否在租")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    def calculate_remaining_days(self):
        """计算剩余天数"""
        if self.fee_end_date:
            today = timezone.now().date()
            delta = self.fee_end_date - today
            return delta.days
        return 0
    
    def calculate_overdue_days(self):
        """计算逾期天数"""
        today = timezone.now().date()
        if self.fee_end_date and self.fee_end_date < today:
            delta = today - self.fee_end_date
            return delta.days
        return 0
    
    def is_fee_expired(self):
        """判断物业费是否过期"""
        return self.calculate_remaining_days() <= 0
    
    @classmethod
    def calculate_property_fee(cls, area, months=1):
        """计算物业费
        
        规则:
        1. 商铺每平米1.5元
        """
        # 基础费用
        base_fee = float(area) * 1.5
        
        # 总费用
        total_fee = base_fee * months
        
        return round(total_fee, 2)
    
    def __str__(self):
        return f"{self.shop_number}-{self.tenant_name}"
    
    class Meta:
        verbose_name = "商铺"
        verbose_name_plural = "商铺租房管理"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['shop_number']),
            models.Index(fields=['tenant_name']),
            models.Index(fields=['fee_end_date']),
        ]


class ShopCheckout(models.Model):
    """商铺退租记录"""
    shop_number = models.CharField(max_length=20, verbose_name="商铺号")
    tenant_name = models.CharField(max_length=50, verbose_name="租户姓名")
    phone = models.CharField(max_length=20, verbose_name="电话")
    area = models.DecimalField(max_digits=7, decimal_places=2, verbose_name="平米数")
    checkout_date = models.DateField(verbose_name="退租日期")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    
    def __str__(self):
        return f"{self.tenant_name} - {self.checkout_date}"
    
    class Meta:
        verbose_name = "商铺退租"
        verbose_name_plural = "商铺退房列表"
        ordering = ['-checkout_date']


class ShopFeePayment(models.Model):
    """商铺物业费支付记录"""
    shop_number = models.CharField(max_length=20, verbose_name="商铺号")
    tenant_name = models.CharField(max_length=50, verbose_name="租户姓名")
    phone = models.CharField(max_length=20, verbose_name="电话")
    area = models.DecimalField(max_digits=7, decimal_places=2, verbose_name="平米数")
    payment_date = models.DateField(verbose_name="交费时间")
    start_date = models.DateField(verbose_name="物业费开始时间")
    end_date = models.DateField(verbose_name="物业费结束时间")
    amount = models.DecimalField(max_digits=8, decimal_places=2, verbose_name="金额")
    months = models.IntegerField(default=1, verbose_name="缴费月数")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    
    def __str__(self):
        return f"{self.tenant_name} - {self.payment_date} - ¥{self.amount}"
    
    class Meta:
        verbose_name = "商铺物业费"
        verbose_name_plural = "商铺物业费流水"
        ordering = ['-payment_date'] 