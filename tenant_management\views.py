from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q
from django_filters.rest_framework import DjangoFilterBackend
from datetime import datetime, timedelta

from .models import Tenant, TenantCheckout, PropertyFeePayment
from .serializers import (
    TenantSerializer, TenantListSerializer, TenantExpiredSerializer,
    TenantCheckoutSerializer, PropertyFeePaymentSerializer, RenewFeeSerializer
)


class TenantViewSet(viewsets.ModelViewSet):
    """租客管理ViewSet"""
    queryset = Tenant.objects.all()
    serializer_class = TenantSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['building_number', 'room_number', 'tenant_name', 'is_active']
    search_fields = ['building_number', 'room_number', 'tenant_name', 'id_card', 'owner_name']
    ordering_fields = ['building_number', 'room_number', 'fee_end_date', 'created_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """根据情况返回不同的查询集"""
        queryset = Tenant.objects.filter(is_active=True)
        return queryset

    def get_serializer_class(self):
        """根据不同的请求返回不同的序列化器"""
        if self.action == 'list':
            return TenantListSerializer
        return TenantSerializer
        
    def create(self, request, *args, **kwargs):
        """创建租客记录"""
        # 获取房号自动计算平米数
        if 'room_number' in request.data and not request.data.get('area'):
            area = Tenant.get_area_by_room(request.data['room_number'])
            request.data['area'] = area
        
        # 计算物业费
        if all(k in request.data for k in ('building_number', 'room_number', 'area')):
            building_number = request.data['building_number']
            room_number = request.data['room_number']
            area = request.data['area']
            property_fee = Tenant.calculate_property_fee(building_number, room_number, area)
            request.data['property_fee'] = property_fee
            
        return super().create(request, *args, **kwargs)
        
    def update(self, request, *args, **kwargs):
        """更新租客记录"""
        # 获取房号自动计算平米数
        if 'room_number' in request.data and not request.data.get('area'):
            area = Tenant.get_area_by_room(request.data['room_number'])
            request.data['area'] = area
            
        # 重新计算物业费
        if all(k in request.data for k in ('building_number', 'room_number', 'area')):
            building_number = request.data['building_number']
            room_number = request.data['room_number']
            area = request.data['area']
            property_fee = Tenant.calculate_property_fee(building_number, room_number, area)
            request.data['property_fee'] = property_fee
            
        return super().update(request, *args, **kwargs)
    
    @action(detail=True, methods=['post'])
    def renew_fee(self, request, pk=None):
        """续费"""
        tenant = self.get_object()
        serializer = RenewFeeSerializer(data=request.data)
        
        if serializer.is_valid():
            months = serializer.validated_data['months']
            
            # 计算费用
            property_fee = Tenant.calculate_property_fee(
                tenant.building_number, 
                tenant.room_number, 
                tenant.area, 
                months
            )
            
            # 计算新的到期时间
            current_date = tenant.fee_end_date
            if current_date < timezone.now().date():
                # 如果已过期，从今天开始计算
                current_date = timezone.now().date()
                
            new_end_date = current_date + timedelta(days=30*months)
            
            # 创建支付记录
            payment = PropertyFeePayment.objects.create(
                building_number=tenant.building_number,
                room_number=tenant.room_number,
                area=tenant.area,
                tenant_name=tenant.tenant_name,
                phone=tenant.owner_phone,
                payment_date=timezone.now().date(),
                start_date=current_date,
                end_date=new_end_date,
                amount=property_fee,
                months=months
            )
            
            # 更新租客的物业费到期时间
            tenant.fee_end_date = new_end_date
            tenant.save()
            
            return Response({
                'success': True,
                'message': f'已成功续费{months}个月',
                'new_end_date': new_end_date,
                'payment_id': payment.id
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def checkout(self, request, pk=None):
        """退房"""
        tenant = self.get_object()
        
        # 创建退房记录
        TenantCheckout.objects.create(
            building_number=tenant.building_number,
            room_number=tenant.room_number,
            area=tenant.area,
            tenant_name=tenant.tenant_name,
            phone=tenant.owner_phone,
            checkout_date=timezone.now().date()
        )
        
        # 标记租客为非活跃
        tenant.is_active = False
        tenant.save()
        
        return Response({
            'success': True,
            'message': f'{tenant.tenant_name}已退房'
        })
        

class ExpiredFeeViewSet(viewsets.ReadOnlyModelViewSet):
    """物业费到期视图"""
    serializer_class = TenantExpiredSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['building_number', 'room_number', 'tenant_name']
    search_fields = ['building_number', 'room_number', 'tenant_name']
    ordering_fields = ['building_number', 'room_number', 'fee_end_date']
    ordering = ['fee_end_date']
    
    def get_queryset(self):
        today = timezone.now().date()
        return Tenant.objects.filter(
            is_active=True,
            fee_end_date__lt=today
        )


class CheckoutViewSet(viewsets.ModelViewSet):
    """退房记录视图"""
    queryset = TenantCheckout.objects.all()
    serializer_class = TenantCheckoutSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['building_number', 'room_number', 'tenant_name']
    search_fields = ['building_number', 'room_number', 'tenant_name']
    ordering_fields = ['building_number', 'room_number', 'checkout_date']
    ordering = ['-checkout_date']


class FeePaymentViewSet(viewsets.ModelViewSet):
    """物业费支付记录视图"""
    queryset = PropertyFeePayment.objects.all()
    serializer_class = PropertyFeePaymentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['building_number', 'room_number', 'tenant_name']
    search_fields = ['building_number', 'room_number', 'tenant_name']
    ordering_fields = ['building_number', 'room_number', 'payment_date']
    ordering = ['-payment_date'] 