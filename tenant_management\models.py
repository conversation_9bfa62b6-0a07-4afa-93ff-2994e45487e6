from django.db import models
from django.utils import timezone
from datetime import timedelta
import datetime

class Tenant(models.Model):
    """租客模型"""
    building_number = models.CharField(max_length=10, verbose_name="楼号")
    room_number = models.CharField(max_length=10, verbose_name="房号")
    area = models.DecimalField(max_digits=7, decimal_places=2, verbose_name="平米数")
    tenant_name = models.CharField(max_length=50, verbose_name="租客姓名")
    id_card = models.CharField(max_length=18, verbose_name="身份证号")
    resident_count = models.IntegerField(default=1, verbose_name="租住人数")
    owner_name = models.CharField(max_length=50, verbose_name="房东姓名")
    owner_phone = models.CharField(max_length=20, verbose_name="房东电话")
    move_in_date = models.DateField(verbose_name="入住时间")
    fee_end_date = models.DateField(verbose_name="物业费到期时间")
    property_fee = models.DecimalField(max_digits=8, decimal_places=2, verbose_name="物业费用")
    is_active = models.BooleanField(default=True, verbose_name="是否在住")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    def calculate_remaining_days(self):
        """计算剩余天数"""
        if self.fee_end_date:
            today = timezone.now().date()
            delta = self.fee_end_date - today
            return delta.days
        return 0
    
    def calculate_overdue_days(self):
        """计算逾期天数"""
        today = timezone.now().date()
        if self.fee_end_date and self.fee_end_date < today:
            delta = today - self.fee_end_date
            return delta.days
        return 0
    
    def is_fee_expired(self):
        """判断物业费是否过期"""
        return self.calculate_remaining_days() <= 0
    
    @classmethod
    def calculate_property_fee(cls, building_number, room_number, area, months=1):
        """计算物业费
        
        规则:
        1. 基础费用: 每平米1元
        2. 电梯费: 11层以下(含11层)每平米0.3元, 11层以上每平米0.35元
        """
        # 基础费用
        base_fee = float(area) * 1
        
        # 根据房号判断楼层
        try:
            floor = int(room_number[:2])
        except (ValueError, IndexError):
            floor = 1
        
        # 电梯费
        if floor <= 11:
            elevator_fee = float(area) * 0.3
        else:
            elevator_fee = float(area) * 0.35
        
        # 总费用
        total_fee = (base_fee + elevator_fee) * months
        
        return round(total_fee, 2)
    
    @classmethod
    def get_area_by_room(cls, room_number):
        """根据房号自动计算平米数"""
        # 提取房号的最后两位数字
        try:
            last_digits = room_number[-2:]
        except IndexError:
            return 90  # 默认值
            
        if last_digits in ('01', '04'):
            return 130
        elif last_digits in ('02', '03'):
            return 90
        else:
            return 90  # 默认值
    
    def __str__(self):
        return f"{self.building_number}栋{self.room_number}-{self.tenant_name}"
    
    class Meta:
        verbose_name = "租客"
        verbose_name_plural = "租客管理"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['building_number', 'room_number']),
            models.Index(fields=['tenant_name']),
            models.Index(fields=['fee_end_date']),
        ]


class TenantCheckout(models.Model):
    """租客退房记录"""
    building_number = models.CharField(max_length=10, verbose_name="楼号")
    room_number = models.CharField(max_length=10, verbose_name="房号")
    area = models.DecimalField(max_digits=7, decimal_places=2, verbose_name="平米数")
    tenant_name = models.CharField(max_length=50, verbose_name="租客姓名")
    phone = models.CharField(max_length=20, verbose_name="电话")
    checkout_date = models.DateField(verbose_name="退房日期")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    
    def __str__(self):
        return f"{self.tenant_name} - {self.checkout_date}"
    
    class Meta:
        verbose_name = "租客退房"
        verbose_name_plural = "租客退房列表"
        ordering = ['-checkout_date']


class PropertyFeePayment(models.Model):
    """物业费支付记录"""
    building_number = models.CharField(max_length=10, verbose_name="楼号")
    room_number = models.CharField(max_length=10, verbose_name="房号")
    area = models.DecimalField(max_digits=7, decimal_places=2, verbose_name="平米数")
    tenant_name = models.CharField(max_length=50, verbose_name="租客姓名")
    phone = models.CharField(max_length=20, verbose_name="电话")
    payment_date = models.DateField(verbose_name="交费时间")
    start_date = models.DateField(verbose_name="物业费开始时间")
    end_date = models.DateField(verbose_name="物业费结束时间")
    amount = models.DecimalField(max_digits=8, decimal_places=2, verbose_name="金额")
    months = models.IntegerField(default=1, verbose_name="缴费月数")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    
    def __str__(self):
        return f"{self.tenant_name} - {self.payment_date} - ¥{self.amount}"
    
    class Meta:
        verbose_name = "物业费支付"
        verbose_name_plural = "物业费流水统计"
        ordering = ['-payment_date'] 