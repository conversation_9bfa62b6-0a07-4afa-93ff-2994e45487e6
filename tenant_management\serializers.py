from rest_framework import serializers
from .models import Tenant, TenantCheckout, PropertyFeePayment


class TenantSerializer(serializers.ModelSerializer):
    remaining_days = serializers.SerializerMethodField()
    
    class Meta:
        model = Tenant
        fields = '__all__'
    
    def get_remaining_days(self, obj):
        return obj.calculate_remaining_days()


class TenantListSerializer(serializers.ModelSerializer):
    remaining_days = serializers.SerializerMethodField()
    
    class Meta:
        model = Tenant
        fields = [
            'id', 'building_number', 'room_number', 'area', 'tenant_name',
            'id_card', 'resident_count', 'owner_name', 'owner_phone',
            'fee_end_date', 'remaining_days', 'is_active'
        ]
    
    def get_remaining_days(self, obj):
        return obj.calculate_remaining_days()


class TenantExpiredSerializer(serializers.ModelSerializer):
    overdue_days = serializers.SerializerMethodField()
    
    class Meta:
        model = Tenant
        fields = [
            'id', 'building_number', 'room_number', 'area', 'tenant_name',
            'owner_phone', 'fee_end_date', 'overdue_days'
        ]
    
    def get_overdue_days(self, obj):
        return obj.calculate_overdue_days()


class TenantCheckoutSerializer(serializers.ModelSerializer):
    class Meta:
        model = TenantCheckout
        fields = '__all__'


class PropertyFeePaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = PropertyFeePayment
        fields = '__all__'


class RenewFeeSerializer(serializers.Serializer):
    tenant_id = serializers.IntegerField()
    months = serializers.IntegerField(min_value=1) 