# 东悦物业管理系统

这是一个基于Django开发的物业管理系统，提供租客管理、商品房管理、商铺信息管理和地下车位出租管理功能。

## 功能模块

系统包含以下主要模块：

1. **租客管理**
   - 租客列表
   - 物业费到期列表
   - 租客退房列表
   - 租客物业费流水统计

2. **商品房管理**
   - 商品房物业费列表
   - 物业费到期列表
   - 商品房物业费流水统计

3. **商铺信息管理**
   - 商铺租房管理
   - 商铺物业费到期列表
   - 商铺退房列表
   - 商铺物业费流水

4. **地下车位出租管理**
   - 地下车位出租列表
   - 到期列表
   - 退车位列表
   - 车位物业费流水

## 技术栈

- **后端**：Django 4.2
- **前端**：Bootstrap 5.3, Chart.js
- **数据库**：MySQL

## 安装和运行

### 环境要求
- Python 3.8+
- MySQL 5.7+

### 安装依赖
```bash
pip install -r requirements.txt
```

### 配置数据库
1. 在MySQL中创建数据库：
```sql
CREATE DATABASE property_system CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
```

2. 在`property_system/settings.py`中配置数据库连接信息：
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'property_system',
        'USER': 'your_username',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '3306',
    }
}
```

### 迁移数据库
```bash
python manage.py migrate
```

### 创建超级用户
```bash
python manage.py createsuperuser
```

### 运行开发服务器
```bash
python manage.py runserver
```

访问 http://127.0.0.1:8000/ 进入系统。

## 系统登录

默认用户名：东悦物业
默认密码：123456

## 物业费计算规则

### 租客物业费
- 基础费用：每平米1元
- 电梯费：11层以下(含11层)每平米0.3元，11层以上每平米0.35元
- 房号规则：尾数01或04的为130平米，尾数02或03的为90平米

### 商品房物业费
- 基础费用：每平米1元
- 电梯费：11层以下(含11层)每平米0.3元，11层以上每平米0.35元
- 地下车位管理费：每月20元
- 共用设施设备运行维护费：每月10元

### 商铺物业费
- 基础费用：每平米1.5元

### 车位费
- 车位费：每月20元 