from django.db import models
from django.utils import timezone
from datetime import timedelta


class ParkingSpace(models.Model):
    """地下车位模型"""
    building_number = models.CharField(max_length=10, verbose_name="楼号")
    room_number = models.CharField(max_length=10, verbose_name="房号")
    tenant_name = models.CharField(max_length=50, verbose_name="姓名")
    phone = models.CharField(max_length=20, verbose_name="电话")
    parking_number = models.CharField(max_length=20, verbose_name="车位号")
    license_plate = models.CharField(max_length=20, verbose_name="车牌号")
    rent_start_date = models.DateField(verbose_name="租车位时间")
    fee_end_date = models.DateField(verbose_name="物业费到期时间")
    property_fee = models.DecimalField(max_digits=8, decimal_places=2, verbose_name="物业费")
    is_active = models.BooleanField(default=True, verbose_name="是否在租")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    def calculate_remaining_days(self):
        """计算剩余天数"""
        if self.fee_end_date:
            today = timezone.now().date()
            delta = self.fee_end_date - today
            return delta.days
        return 0
    
    def calculate_overdue_days(self):
        """计算逾期天数"""
        today = timezone.now().date()
        if self.fee_end_date and self.fee_end_date < today:
            delta = today - self.fee_end_date
            return delta.days
        return 0
    
    def is_fee_expired(self):
        """判断物业费是否过期"""
        return self.calculate_remaining_days() <= 0
    
    @classmethod
    def calculate_property_fee(cls, months=1):
        """计算车位费
        
        规则:
        1. 地下车位费是每月20元
        """
        # 车位费
        fee_per_month = 20
        
        # 总费用
        total_fee = fee_per_month * months
        
        return round(total_fee, 2)
    
    def __str__(self):
        return f"{self.building_number}栋{self.room_number}-{self.tenant_name}-{self.parking_number}"
    
    class Meta:
        verbose_name = "车位"
        verbose_name_plural = "地下车位出租列表"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['building_number', 'room_number']),
            models.Index(fields=['tenant_name']),
            models.Index(fields=['parking_number']),
            models.Index(fields=['license_plate']),
            models.Index(fields=['fee_end_date']),
        ]


class ParkingCheckout(models.Model):
    """车位退租记录"""
    building_number = models.CharField(max_length=10, verbose_name="楼号")
    room_number = models.CharField(max_length=10, verbose_name="房号")
    tenant_name = models.CharField(max_length=50, verbose_name="姓名")
    phone = models.CharField(max_length=20, verbose_name="电话")
    parking_number = models.CharField(max_length=20, verbose_name="车位号")
    license_plate = models.CharField(max_length=20, verbose_name="车牌号", blank=True, null=True)
    checkout_date = models.DateField(verbose_name="退租日期")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    
    def __str__(self):
        return f"{self.tenant_name} - {self.parking_number} - {self.checkout_date}"
    
    class Meta:
        verbose_name = "车位退租"
        verbose_name_plural = "退车位列表"
        ordering = ['-checkout_date']


class ParkingFeePayment(models.Model):
    """车位物业费支付记录"""
    building_number = models.CharField(max_length=10, verbose_name="楼号")
    room_number = models.CharField(max_length=10, verbose_name="房号")
    tenant_name = models.CharField(max_length=50, verbose_name="姓名")
    phone = models.CharField(max_length=20, verbose_name="电话")
    parking_number = models.CharField(max_length=20, verbose_name="车位号")
    license_plate = models.CharField(max_length=20, verbose_name="车牌号", blank=True, null=True)
    payment_date = models.DateField(verbose_name="交费时间")
    start_date = models.DateField(verbose_name="物业费开始时间")
    end_date = models.DateField(verbose_name="物业费结束时间")
    amount = models.DecimalField(max_digits=8, decimal_places=2, verbose_name="金额")
    months = models.IntegerField(default=1, verbose_name="缴费月数")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    
    def __str__(self):
        return f"{self.tenant_name} - {self.parking_number} - {self.payment_date} - ¥{self.amount}"
    
    class Meta:
        verbose_name = "车位物业费"
        verbose_name_plural = "车位物业费流水"
        ordering = ['-payment_date'] 