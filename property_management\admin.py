from django.contrib import admin
from .models import Property, PropertyFeePayment


@admin.register(Property)
class PropertyAdmin(admin.ModelAdmin):
    list_display = ('building_number', 'unit_number', 'room_number', 'area', 
                    'owner_name', 'has_basement', 'has_parking', 'owner_phone',
                    'property_fee', 'fee_end_date')
    list_filter = ('building_number', 'unit_number', 'has_basement', 'has_parking')
    search_fields = ('building_number', 'unit_number', 'room_number', 'owner_name', 'owner_phone')
    list_per_page = 20


@admin.register(PropertyFeePayment)
class PropertyFeePaymentAdmin(admin.ModelAdmin):
    list_display = ('building_number', 'unit_number', 'room_number', 
                    'owner_name', 'payment_date', 'start_date', 'end_date', 'amount')
    list_filter = ('building_number', 'unit_number', 'payment_date')
    search_fields = ('building_number', 'unit_number', 'room_number', 'owner_name')
    list_per_page = 20 