from django.contrib import admin
from .models import Tenant, TenantCheckout, PropertyFeePayment


@admin.register(Tenant)
class TenantAdmin(admin.ModelAdmin):
    list_display = ('building_number', 'room_number', 'tenant_name', 'id_card', 
                    'owner_name', 'owner_phone', 'fee_end_date', 'is_active')
    list_filter = ('building_number', 'is_active')
    search_fields = ('building_number', 'room_number', 'tenant_name', 'id_card', 'owner_name', 'owner_phone')
    list_per_page = 20


@admin.register(TenantCheckout)
class TenantCheckoutAdmin(admin.ModelAdmin):
    list_display = ('building_number', 'room_number', 'tenant_name', 'checkout_date')
    list_filter = ('building_number', 'checkout_date')
    search_fields = ('building_number', 'room_number', 'tenant_name')
    list_per_page = 20


@admin.register(PropertyFeePayment)
class PropertyFeePaymentAdmin(admin.ModelAdmin):
    list_display = ('building_number', 'room_number', 'tenant_name', 'payment_date', 
                    'start_date', 'end_date', 'amount')
    list_filter = ('building_number', 'payment_date')
    search_fields = ('building_number', 'room_number', 'tenant_name')
    list_per_page = 20 