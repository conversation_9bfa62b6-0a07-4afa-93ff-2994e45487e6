from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods


def login_view(request):
    """登录视图"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        if username == '东悦物业' and password == '123456':
            # 使用Django的认证系统
            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)
                return redirect('dashboard')
            else:
                # 如果用户不存在，但凭据是正确的特定值，则视为登录成功
                # 实际应用中应该使用更安全的方式，这里仅做演示
                return redirect('dashboard')
        
        error_message = '用户名或密码错误'
        return render(request, 'auth/login.html', {'error_message': error_message})
    
    return render(request, 'auth/login.html')


def logout_view(request):
    """退出登录视图"""
    logout(request)
    return redirect('login')


@login_required
def dashboard(request):
    """仪表板首页"""
    return render(request, 'base/dashboard.html')


@login_required
def statistics_view(request):
    """统计数据API视图"""
    # 简单的模拟数据，实际应用中应从数据库获取
    data = {
        'tenant_count': 125,
        'property_count': 350,
        'shop_count': 30,
        'parking_count': 80,
        'overdue_count': 15,
        'monthly_revenue': 58600,
        'chart_data': {
            'labels': ['一月', '二月', '三月', '四月', '五月', '六月'],
            'tenant_data': [65, 59, 80, 81, 56, 55],
            'property_data': [28, 48, 40, 19, 86, 27],
            'shop_data': [10, 15, 8, 12, 15, 18],
            'parking_data': [45, 42, 50, 48, 55, 60]
        }
    }
    return JsonResponse(data) 