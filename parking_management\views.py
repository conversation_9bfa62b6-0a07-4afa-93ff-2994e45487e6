from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q
from django_filters.rest_framework import DjangoFilterBackend
from datetime import datetime, timedelta

from .models import ParkingSpace, ParkingCheckout, ParkingFeePayment
from .serializers import (
    ParkingSpaceSerializer, ParkingListSerializer, ParkingExpiredSerializer,
    ParkingCheckoutSerializer, ParkingFeePaymentSerializer, RenewFeeSerializer
)


class ParkingSpaceViewSet(viewsets.ModelViewSet):
    """车位管理ViewSet"""
    queryset = ParkingSpace.objects.all()
    serializer_class = ParkingSpaceSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['building_number', 'room_number', 'parking_number', 'license_plate', 'is_active']
    search_fields = ['building_number', 'room_number', 'tenant_name', 'phone', 'parking_number', 'license_plate']
    ordering_fields = ['building_number', 'room_number', 'parking_number', 'fee_end_date', 'created_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """根据情况返回不同的查询集"""
        queryset = ParkingSpace.objects.filter(is_active=True)
        return queryset

    def get_serializer_class(self):
        """根据不同的请求返回不同的序列化器"""
        if self.action == 'list':
            return ParkingListSerializer
        return ParkingSpaceSerializer
        
    def create(self, request, *args, **kwargs):
        """创建车位记录"""
        # 计算物业费
        property_fee = ParkingSpace.calculate_property_fee()
        request.data['property_fee'] = property_fee
            
        return super().create(request, *args, **kwargs)
        
    def update(self, request, *args, **kwargs):
        """更新车位记录"""
        # 重新计算物业费
        property_fee = ParkingSpace.calculate_property_fee()
        request.data['property_fee'] = property_fee
            
        return super().update(request, *args, **kwargs)
    
    @action(detail=True, methods=['post'])
    def renew_fee(self, request, pk=None):
        """续费"""
        parking = self.get_object()
        serializer = RenewFeeSerializer(data=request.data)
        
        if serializer.is_valid():
            months = serializer.validated_data['months']
            
            # 计算费用
            property_fee = ParkingSpace.calculate_property_fee(months)
            
            # 计算新的到期时间
            current_date = parking.fee_end_date
            if current_date < timezone.now().date():
                # 如果已过期，从今天开始计算
                current_date = timezone.now().date()
                
            new_end_date = current_date + timedelta(days=30*months)
            
            # 创建支付记录
            payment = ParkingFeePayment.objects.create(
                building_number=parking.building_number,
                room_number=parking.room_number,
                tenant_name=parking.tenant_name,
                phone=parking.phone,
                parking_number=parking.parking_number,
                license_plate=parking.license_plate,
                payment_date=timezone.now().date(),
                start_date=current_date,
                end_date=new_end_date,
                amount=property_fee,
                months=months
            )
            
            # 更新车位的物业费到期时间
            parking.fee_end_date = new_end_date
            parking.save()
            
            return Response({
                'success': True,
                'message': f'已成功续费{months}个月',
                'new_end_date': new_end_date,
                'payment_id': payment.id
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def checkout(self, request, pk=None):
        """退租"""
        parking = self.get_object()
        
        # 创建退租记录
        ParkingCheckout.objects.create(
            building_number=parking.building_number,
            room_number=parking.room_number,
            tenant_name=parking.tenant_name,
            phone=parking.phone,
            parking_number=parking.parking_number,
            license_plate=parking.license_plate,
            checkout_date=timezone.now().date()
        )
        
        # 标记车位为非活跃
        parking.is_active = False
        parking.save()
        
        return Response({
            'success': True,
            'message': f'{parking.tenant_name}的{parking.parking_number}车位已退租'
        })
        

class ExpiredFeeViewSet(viewsets.ReadOnlyModelViewSet):
    """车位物业费到期视图"""
    serializer_class = ParkingExpiredSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['building_number', 'room_number', 'parking_number', 'license_plate']
    search_fields = ['building_number', 'room_number', 'tenant_name', 'phone', 'parking_number', 'license_plate']
    ordering_fields = ['building_number', 'room_number', 'parking_number', 'fee_end_date']
    ordering = ['fee_end_date']
    
    def get_queryset(self):
        today = timezone.now().date()
        return ParkingSpace.objects.filter(
            is_active=True,
            fee_end_date__lt=today
        )


class CheckoutViewSet(viewsets.ModelViewSet):
    """车位退租记录视图"""
    queryset = ParkingCheckout.objects.all()
    serializer_class = ParkingCheckoutSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['building_number', 'room_number', 'parking_number', 'license_plate']
    search_fields = ['building_number', 'room_number', 'tenant_name', 'phone', 'parking_number', 'license_plate']
    ordering_fields = ['building_number', 'room_number', 'parking_number', 'checkout_date']
    ordering = ['-checkout_date']


class FeePaymentViewSet(viewsets.ModelViewSet):
    """车位物业费支付记录视图"""
    queryset = ParkingFeePayment.objects.all()
    serializer_class = ParkingFeePaymentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['building_number', 'room_number', 'parking_number', 'license_plate']
    search_fields = ['building_number', 'room_number', 'tenant_name', 'phone', 'parking_number', 'license_plate']
    ordering_fields = ['building_number', 'room_number', 'parking_number', 'payment_date']
    ordering = ['-payment_date'] 