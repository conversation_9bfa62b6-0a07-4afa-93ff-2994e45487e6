<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>东悦物业管理系统 - 仪表板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        :root {
            --sidebar-width: 250px;
            --primary-color: #4e73df;
            --secondary-color: #5a5c69;
            --success-color: #1cc88a;
            --danger-color: #e74a3b;
            --warning-color: #f6c23e;
            --info-color: #36b9cc;
        }
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f8f9fc;
            overflow-x: hidden;
        }
        #wrapper {
            display: flex;
        }
        #sidebar {
            width: var(--sidebar-width);
            min-height: 100vh;
            background: linear-gradient(180deg, var(--primary-color) 10%, #224abe 100%);
            color: white;
            position: fixed;
            z-index: 1000;
            transition: all 0.3s;
        }
        #sidebar.collapsed {
            margin-left: calc(-1 * var(--sidebar-width));
        }
        .sidebar-brand {
            height: 4.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1.5rem 1rem;
            font-size: 1.2rem;
            font-weight: 800;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .sidebar-heading {
            padding: 0.875rem 1.25rem;
            font-size: 0.75rem;
            text-transform: uppercase;
            color: rgba(255,255,255,0.6);
        }
        .nav-item {
            position: relative;
        }
        .nav-link {
            padding: 0.75rem 1rem;
            color: rgba(255,255,255,0.8);
            font-weight: 500;
            display: flex;
            align-items: center;
        }
        .nav-link:hover {
            color: #fff;
            background-color: rgba(255,255,255,0.1);
        }
        .nav-link i {
            margin-right: 0.5rem;
        }
        .nav-link.active {
            color: white;
            font-weight: 700;
            background-color: rgba(255,255,255,0.1);
            border-left: 4px solid white;
        }
        #content {
            flex: 1;
            margin-left: var(--sidebar-width);
            transition: all 0.3s;
        }
        #content.expanded {
            margin-left: 0;
        }
        .topbar {
            height: 4.375rem;
            background-color: white;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem;
        }
        .content-wrapper {
            padding: 1.5rem;
        }
        .card {
            border: none;
            border-radius: 0.35rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
            margin-bottom: 1.5rem;
        }
        .card-header {
            background-color: white;
            border-bottom: 1px solid #e3e6f0;
            padding: 0.75rem 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        .dropdown-menu {
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        .stat-card {
            border-left: 4px solid var(--primary-color);
        }
        .stat-card.primary {
            border-left-color: var(--primary-color);
        }
        .stat-card.success {
            border-left-color: var(--success-color);
        }
        .stat-card.danger {
            border-left-color: var(--danger-color);
        }
        .stat-card.warning {
            border-left-color: var(--warning-color);
        }
        .stat-card .icon {
            color: #dddfeb;
            font-size: 2rem;
        }
        .stat-card .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--secondary-color);
        }
        .stat-card .stat-label {
            font-size: 0.8rem;
            text-transform: uppercase;
            font-weight: 700;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div id="wrapper">
        <!-- 侧边栏 -->
        <div id="sidebar">
            <div class="sidebar-brand">
                <i class="bi bi-building me-2"></i>
                东悦物业管理系统
            </div>
            <hr class="sidebar-divider my-0">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a href="{% url 'dashboard' %}" class="nav-link active">
                        <i class="bi bi-speedometer2"></i>
                        仪表板
                    </a>
                </li>
                <div class="sidebar-heading">
                    租客管理
                </div>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-people"></i>
                        租客列表
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-exclamation-triangle"></i>
                        物业费到期列表
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-box-arrow-left"></i>
                        租客退房列表
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-cash-coin"></i>
                        物业费流水统计
                    </a>
                </li>
                <div class="sidebar-heading">
                    商品房管理
                </div>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-house"></i>
                        商品房物业费列表
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-exclamation-triangle"></i>
                        物业费到期列表
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-cash-coin"></i>
                        物业费流水统计
                    </a>
                </li>
                <div class="sidebar-heading">
                    商铺管理
                </div>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-shop"></i>
                        商铺租房管理
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-exclamation-triangle"></i>
                        商铺物业费到期列表
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-box-arrow-left"></i>
                        商铺退房列表
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-cash-coin"></i>
                        商铺物业费流水
                    </a>
                </li>
                <div class="sidebar-heading">
                    车位管理
                </div>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-p-square"></i>
                        地下车位出租列表
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-exclamation-triangle"></i>
                        到期列表
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-box-arrow-left"></i>
                        退车位列表
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="bi bi-cash-coin"></i>
                        车位物业费流水
                    </a>
                </li>
            </ul>
            <hr class="sidebar-divider d-none d-md-block">
        </div>

        <!-- 主内容区 -->
        <div id="content">
            <nav class="topbar navbar navbar-expand navbar-light bg-white">
                <!-- 侧边栏切换按钮 -->
                <button id="sidebarToggle" class="btn btn-link d-md-none rounded-circle me-3">
                    <i class="bi bi-list"></i>
                </button>

                <!-- 欢迎文本 -->
                <div class="d-none d-md-inline-block ml-auto me-3">
                    <h4 class="mb-0">欢迎回来, 管理员</h4>
                </div>

                <ul class="navbar-nav ml-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <span class="d-none d-lg-inline text-gray-600 small me-2">东悦物业</span>
                            <i class="bi bi-person-circle"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-person me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'logout' %}"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </nav>

            <div class="content-wrapper">
                <div class="container-fluid">
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">仪表板</h1>
                    </div>

                    <div class="row">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stat-card primary h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="stat-label">租客总数</div>
                                            <div class="stat-value" id="tenant-count">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="bi bi-people icon"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stat-card success h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="stat-label">商品房总数</div>
                                            <div class="stat-value" id="property-count">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="bi bi-house icon"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stat-card warning h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="stat-label">商铺总数</div>
                                            <div class="stat-value" id="shop-count">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="bi bi-shop icon"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stat-card danger h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="stat-label">车位总数</div>
                                            <div class="stat-value" id="parking-count">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="bi bi-p-square icon"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xl-8 col-lg-7">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <i class="bi bi-bar-chart me-1"></i>
                                    月度统计
                                </div>
                                <div class="card-body">
                                    <canvas id="myChart" width="100%" height="40"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-4 col-lg-5">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <i class="bi bi-info-circle me-1"></i>
                                    物业费到期情况
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-warning">
                                        <strong id="overdue-count">0</strong> 个客户的物业费已到期
                                    </div>
                                    <div class="text-center mt-4">
                                        <h5>本月物业费收入</h5>
                                        <h2 class="text-primary" id="monthly-revenue">¥0</h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
    <script>
        // 侧边栏切换功能
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('collapsed');
            document.getElementById('content').classList.toggle('expanded');
        });

        // 获取统计数据
        fetch('/api/statistics/')
            .then(response => response.json())
            .then(data => {
                document.getElementById('tenant-count').textContent = data.tenant_count;
                document.getElementById('property-count').textContent = data.property_count;
                document.getElementById('shop-count').textContent = data.shop_count;
                document.getElementById('parking-count').textContent = data.parking_count;
                document.getElementById('overdue-count').textContent = data.overdue_count;
                document.getElementById('monthly-revenue').textContent = '¥' + data.monthly_revenue;
                
                // 创建图表
                const ctx = document.getElementById('myChart').getContext('2d');
                const myChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: data.chart_data.labels,
                        datasets: [
                            {
                                label: '租客',
                                data: data.chart_data.tenant_data,
                                backgroundColor: 'rgba(78, 115, 223, 0.5)',
                                borderColor: 'rgba(78, 115, 223, 1)',
                                borderWidth: 1
                            },
                            {
                                label: '商品房',
                                data: data.chart_data.property_data,
                                backgroundColor: 'rgba(28, 200, 138, 0.5)',
                                borderColor: 'rgba(28, 200, 138, 1)',
                                borderWidth: 1
                            },
                            {
                                label: '商铺',
                                data: data.chart_data.shop_data,
                                backgroundColor: 'rgba(246, 194, 62, 0.5)',
                                borderColor: 'rgba(246, 194, 62, 1)',
                                borderWidth: 1
                            },
                            {
                                label: '车位',
                                data: data.chart_data.parking_data,
                                backgroundColor: 'rgba(231, 74, 59, 0.5)',
                                borderColor: 'rgba(231, 74, 59, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            })
            .catch(error => console.error('Error:', error));
    </script>
</body>
</html> 