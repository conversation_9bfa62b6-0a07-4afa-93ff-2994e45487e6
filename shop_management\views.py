from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q
from django_filters.rest_framework import DjangoFilterBackend
from datetime import datetime, timedelta

from .models import Shop, ShopCheckout, ShopFeePayment
from .serializers import (
    ShopSerializer, ShopListSerializer, ShopExpiredSerializer,
    ShopCheckoutSerializer, ShopFeePaymentSerializer, RenewFeeSerializer
)


class ShopViewSet(viewsets.ModelViewSet):
    """商铺管理ViewSet"""
    queryset = Shop.objects.all()
    serializer_class = ShopSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['shop_number', 'tenant_name', 'is_active']
    search_fields = ['shop_number', 'tenant_name', 'phone', 'id_card']
    ordering_fields = ['shop_number', 'fee_end_date', 'created_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """根据情况返回不同的查询集"""
        queryset = Shop.objects.filter(is_active=True)
        return queryset

    def get_serializer_class(self):
        """根据不同的请求返回不同的序列化器"""
        if self.action == 'list':
            return ShopListSerializer
        return ShopSerializer
        
    def create(self, request, *args, **kwargs):
        """创建商铺记录"""
        # 计算物业费
        if 'area' in request.data:
            area = request.data['area']
            property_fee = Shop.calculate_property_fee(area)
            request.data['property_fee'] = property_fee
            
        return super().create(request, *args, **kwargs)
        
    def update(self, request, *args, **kwargs):
        """更新商铺记录"""
        # 重新计算物业费
        if 'area' in request.data:
            area = request.data['area']
            property_fee = Shop.calculate_property_fee(area)
            request.data['property_fee'] = property_fee
            
        return super().update(request, *args, **kwargs)
    
    @action(detail=True, methods=['post'])
    def renew_fee(self, request, pk=None):
        """续费"""
        shop = self.get_object()
        serializer = RenewFeeSerializer(data=request.data)
        
        if serializer.is_valid():
            months = serializer.validated_data['months']
            
            # 计算费用
            property_fee = Shop.calculate_property_fee(
                shop.area, 
                months
            )
            
            # 计算新的到期时间
            current_date = shop.fee_end_date
            if current_date < timezone.now().date():
                # 如果已过期，从今天开始计算
                current_date = timezone.now().date()
                
            new_end_date = current_date + timedelta(days=30*months)
            
            # 创建支付记录
            payment = ShopFeePayment.objects.create(
                shop_number=shop.shop_number,
                tenant_name=shop.tenant_name,
                phone=shop.phone,
                area=shop.area,
                payment_date=timezone.now().date(),
                start_date=current_date,
                end_date=new_end_date,
                amount=property_fee,
                months=months
            )
            
            # 更新商铺的物业费到期时间
            shop.fee_end_date = new_end_date
            shop.save()
            
            return Response({
                'success': True,
                'message': f'已成功续费{months}个月',
                'new_end_date': new_end_date,
                'payment_id': payment.id
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def checkout(self, request, pk=None):
        """退租"""
        shop = self.get_object()
        
        # 创建退租记录
        ShopCheckout.objects.create(
            shop_number=shop.shop_number,
            tenant_name=shop.tenant_name,
            phone=shop.phone,
            area=shop.area,
            checkout_date=timezone.now().date()
        )
        
        # 标记商铺为非活跃
        shop.is_active = False
        shop.save()
        
        return Response({
            'success': True,
            'message': f'{shop.tenant_name}已退租'
        })
        

class ExpiredFeeViewSet(viewsets.ReadOnlyModelViewSet):
    """商铺物业费到期视图"""
    serializer_class = ShopExpiredSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['shop_number', 'tenant_name']
    search_fields = ['shop_number', 'tenant_name', 'phone']
    ordering_fields = ['shop_number', 'fee_end_date']
    ordering = ['fee_end_date']
    
    def get_queryset(self):
        today = timezone.now().date()
        return Shop.objects.filter(
            is_active=True,
            fee_end_date__lt=today
        )


class ShopCheckoutViewSet(viewsets.ModelViewSet):
    """商铺退租记录视图"""
    queryset = ShopCheckout.objects.all()
    serializer_class = ShopCheckoutSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['shop_number', 'tenant_name']
    search_fields = ['shop_number', 'tenant_name', 'phone']
    ordering_fields = ['shop_number', 'checkout_date']
    ordering = ['-checkout_date']


class FeePaymentViewSet(viewsets.ModelViewSet):
    """商铺物业费支付记录视图"""
    queryset = ShopFeePayment.objects.all()
    serializer_class = ShopFeePaymentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['shop_number', 'tenant_name']
    search_fields = ['shop_number', 'tenant_name', 'phone']
    ordering_fields = ['shop_number', 'payment_date']
    ordering = ['-payment_date'] 