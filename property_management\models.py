from django.db import models
from django.utils import timezone
from datetime import timedelta


class Property(models.Model):
    """商品房模型"""
    building_number = models.CharField(max_length=10, verbose_name="楼号")
    unit_number = models.CharField(max_length=10, verbose_name="单元号")
    room_number = models.CharField(max_length=10, verbose_name="房号")
    area = models.DecimalField(max_digits=7, decimal_places=2, verbose_name="平米数")
    owner_name = models.CharField(max_length=50, verbose_name="业主姓名")
    has_basement = models.BooleanField(default=False, verbose_name="地下室")
    has_parking = models.BooleanField(default=False, verbose_name="车位")
    owner_phone = models.CharField(max_length=20, verbose_name="业主电话")
    property_fee = models.DecimalField(max_digits=8, decimal_places=2, verbose_name="物业费总额")
    fee_end_date = models.DateField(verbose_name="物业费到期日期")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    def calculate_remaining_days(self):
        """计算剩余天数"""
        if self.fee_end_date:
            today = timezone.now().date()
            delta = self.fee_end_date - today
            return delta.days
        return 0
    
    def calculate_overdue_days(self):
        """计算逾期天数"""
        today = timezone.now().date()
        if self.fee_end_date and self.fee_end_date < today:
            delta = today - self.fee_end_date
            return delta.days
        return 0
    
    def is_fee_expired(self):
        """判断物业费是否过期"""
        return self.calculate_remaining_days() <= 0
    
    @classmethod
    def calculate_property_fee(cls, room_number, area, has_basement, has_parking, months=1):
        """计算物业费
        
        规则:
        1. 基础费用: 每平米1元
        2. 电梯费: 11层以下(含11层)每平米0.3元, 11层以上每平米0.35元
        3. 地下车位管理费: 每月20元
        4. 共用设施设备运行维护费: 每月10元
        """
        # 基础费用
        base_fee = float(area) * 1
        
        # 根据房号判断楼层
        try:
            floor = int(room_number[:2])
        except (ValueError, IndexError):
            floor = 1
        
        # 电梯费
        if floor <= 11:
            elevator_fee = float(area) * 0.3
        else:
            elevator_fee = float(area) * 0.35
        
        # 地下车位管理费
        parking_fee = 20 if has_parking else 0
        
        # 共用设施设备运行维护费
        common_fee = 10
        
        # 总费用
        total_fee = (base_fee + elevator_fee + parking_fee + common_fee) * months
        
        return round(total_fee, 2)
    
    def __str__(self):
        return f"{self.building_number}栋{self.unit_number}单元{self.room_number}-{self.owner_name}"
    
    class Meta:
        verbose_name = "商品房"
        verbose_name_plural = "商品房物业费列表"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['building_number', 'unit_number', 'room_number']),
            models.Index(fields=['owner_name']),
            models.Index(fields=['fee_end_date']),
        ]


class PropertyFeePayment(models.Model):
    """商品房物业费支付记录"""
    building_number = models.CharField(max_length=10, verbose_name="楼号")
    unit_number = models.CharField(max_length=10, verbose_name="单元号")
    room_number = models.CharField(max_length=10, verbose_name="房号")
    area = models.DecimalField(max_digits=7, decimal_places=2, verbose_name="平米数")
    owner_name = models.CharField(max_length=50, verbose_name="业主姓名")
    has_basement = models.BooleanField(default=False, verbose_name="地下室")
    has_parking = models.BooleanField(default=False, verbose_name="车位")
    payment_date = models.DateField(verbose_name="交费时间")
    start_date = models.DateField(verbose_name="物业费开始时间")
    end_date = models.DateField(verbose_name="物业费结束时间")
    amount = models.DecimalField(max_digits=8, decimal_places=2, verbose_name="金额")
    months = models.IntegerField(default=1, verbose_name="缴费月数")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    
    def __str__(self):
        return f"{self.owner_name} - {self.payment_date} - ¥{self.amount}"
    
    class Meta:
        verbose_name = "商品房物业费"
        verbose_name_plural = "商品房物业费流水统计"
        ordering = ['-payment_date'] 