<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>东悦物业管理系统 - 登录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            height: 100vh;
            display: flex;
            align-items: center;
            background: linear-gradient(to right, #74ebd5, #ACB6E5);
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .login-container {
            max-width: 400px;
            padding: 40px;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h1 {
            color: #3a3a3a;
            font-weight: bold;
        }
        .btn-login {
            background: linear-gradient(to right, #36d1dc, #5b86e5);
            border: none;
            color: white;
        }
        .btn-login:hover {
            background: linear-gradient(to right, #5b86e5, #36d1dc);
            color: white;
        }
        .form-control:focus {
            border-color: #5b86e5;
            box-shadow: 0 0 0 0.25rem rgba(91, 134, 229, 0.25);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12 col-md-8 col-lg-6">
                <div class="login-container">
                    <div class="logo">
                        <h1>东悦物业管理系统</h1>
                    </div>
                    {% if error_message %}
                    <div class="alert alert-danger" role="alert">
                        {{ error_message }}
                    </div>
                    {% endif %}
                    <form method="post" action="{% url 'login' %}">
                        {% csrf_token %}
                        <div class="mb-4">
                            <label for="username" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-4">
                            <label for="password" class="form-label">密码</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-login btn-lg">登录</button>
                        </div>
                    </form>
                    <div class="text-center mt-4">
                        <p class="text-muted">&copy; 2025 东悦物业管理系统</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 