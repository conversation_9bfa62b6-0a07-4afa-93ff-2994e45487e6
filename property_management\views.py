from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q
from django_filters.rest_framework import DjangoFilterBackend
from datetime import datetime, timedelta

from .models import Property, PropertyFeePayment
from .serializers import (
    PropertySerializer, PropertyListSerializer, PropertyExpiredSerializer,
    PropertyFeePaymentSerializer, RenewFeeSerializer
)


class PropertyViewSet(viewsets.ModelViewSet):
    """商品房管理ViewSet"""
    queryset = Property.objects.all()
    serializer_class = PropertySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['building_number', 'unit_number', 'room_number', 'owner_name']
    search_fields = ['building_number', 'unit_number', 'room_number', 'owner_name', 'owner_phone']
    ordering_fields = ['building_number', 'unit_number', 'room_number', 'fee_end_date', 'created_at']
    ordering = ['building_number', 'unit_number', 'room_number']

    def get_serializer_class(self):
        """根据不同的请求返回不同的序列化器"""
        if self.action == 'list':
            return PropertyListSerializer
        return PropertySerializer
        
    def create(self, request, *args, **kwargs):
        """创建商品房记录"""
        # 计算物业费
        if all(k in request.data for k in ('room_number', 'area')):
            room_number = request.data['room_number']
            area = request.data['area']
            has_basement = request.data.get('has_basement', False)
            has_parking = request.data.get('has_parking', False)
            
            property_fee = Property.calculate_property_fee(room_number, area, has_basement, has_parking)
            request.data['property_fee'] = property_fee
            
        return super().create(request, *args, **kwargs)
        
    def update(self, request, *args, **kwargs):
        """更新商品房记录"""
        # 重新计算物业费
        if all(k in request.data for k in ('room_number', 'area')):
            room_number = request.data['room_number']
            area = request.data['area']
            has_basement = request.data.get('has_basement', False)
            has_parking = request.data.get('has_parking', False)
            
            property_fee = Property.calculate_property_fee(room_number, area, has_basement, has_parking)
            request.data['property_fee'] = property_fee
            
        return super().update(request, *args, **kwargs)
    
    @action(detail=True, methods=['post'])
    def renew_fee(self, request, pk=None):
        """续费"""
        property_obj = self.get_object()
        serializer = RenewFeeSerializer(data=request.data)
        
        if serializer.is_valid():
            months = serializer.validated_data['months']
            
            # 计算费用
            property_fee = Property.calculate_property_fee(
                property_obj.room_number, 
                property_obj.area, 
                property_obj.has_basement, 
                property_obj.has_parking,
                months
            )
            
            # 计算新的到期时间
            current_date = property_obj.fee_end_date
            if current_date < timezone.now().date():
                # 如果已过期，从今天开始计算
                current_date = timezone.now().date()
                
            new_end_date = current_date + timedelta(days=30*months)
            
            # 创建支付记录
            payment = PropertyFeePayment.objects.create(
                building_number=property_obj.building_number,
                unit_number=property_obj.unit_number,
                room_number=property_obj.room_number,
                area=property_obj.area,
                owner_name=property_obj.owner_name,
                has_basement=property_obj.has_basement,
                has_parking=property_obj.has_parking,
                payment_date=timezone.now().date(),
                start_date=current_date,
                end_date=new_end_date,
                amount=property_fee,
                months=months
            )
            
            # 更新商品房的物业费到期时间
            property_obj.fee_end_date = new_end_date
            property_obj.save()
            
            return Response({
                'success': True,
                'message': f'已成功续费{months}个月',
                'new_end_date': new_end_date,
                'payment_id': payment.id
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ExpiredFeeViewSet(viewsets.ReadOnlyModelViewSet):
    """商品房物业费到期视图"""
    serializer_class = PropertyExpiredSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['building_number', 'unit_number', 'room_number', 'owner_name']
    search_fields = ['building_number', 'unit_number', 'room_number', 'owner_name']
    ordering_fields = ['building_number', 'unit_number', 'room_number', 'fee_end_date']
    ordering = ['fee_end_date']
    
    def get_queryset(self):
        today = timezone.now().date()
        return Property.objects.filter(
            fee_end_date__lt=today
        )


class FeePaymentViewSet(viewsets.ModelViewSet):
    """商品房物业费支付记录视图"""
    queryset = PropertyFeePayment.objects.all()
    serializer_class = PropertyFeePaymentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['building_number', 'unit_number', 'room_number', 'owner_name']
    search_fields = ['building_number', 'unit_number', 'room_number', 'owner_name']
    ordering_fields = ['building_number', 'unit_number', 'room_number', 'payment_date']
    ordering = ['-payment_date'] 