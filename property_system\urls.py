"""
URL configuration for property_system project.
"""
from django.contrib import admin
from django.urls import path, include
from django.views.generic import RedirectView
from django.conf import settings
from django.conf.urls.static import static
from .auth_views import login_view, logout_view, dashboard, statistics_view

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', RedirectView.as_view(url='/login/', permanent=False)),
    path('login/', login_view, name='login'),
    path('logout/', logout_view, name='logout'),
    path('dashboard/', dashboard, name='dashboard'),
    path('api/statistics/', statistics_view, name='statistics'),
    
    # API路由
    path('api/tenants/', include('tenant_management.urls')),
    path('api/properties/', include('property_management.urls')),
    path('api/shops/', include('shop_management.urls')),
    path('api/parkings/', include('parking_management.urls')),
]

# 添加静态文件服务
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT) 