from django.contrib import admin
from .models import ParkingSpace, ParkingCheckout, ParkingFeePayment


@admin.register(ParkingSpace)
class ParkingSpaceAdmin(admin.ModelAdmin):
    list_display = ('building_number', 'room_number', 'tenant_name', 'phone', 
                    'parking_number', 'license_plate', 'fee_end_date', 'is_active')
    list_filter = ('building_number', 'is_active')
    search_fields = ('building_number', 'room_number', 'tenant_name', 'phone', 'parking_number', 'license_plate')
    list_per_page = 20


@admin.register(ParkingCheckout)
class ParkingCheckoutAdmin(admin.ModelAdmin):
    list_display = ('building_number', 'room_number', 'tenant_name', 'parking_number', 'checkout_date')
    list_filter = ('building_number', 'checkout_date')
    search_fields = ('building_number', 'room_number', 'tenant_name', 'parking_number')
    list_per_page = 20


@admin.register(ParkingFeePayment)
class ParkingFeePaymentAdmin(admin.ModelAdmin):
    list_display = ('building_number', 'room_number', 'tenant_name', 'parking_number', 
                    'payment_date', 'start_date', 'end_date', 'amount')
    list_filter = ('building_number', 'payment_date')
    search_fields = ('building_number', 'room_number', 'tenant_name', 'parking_number')
    list_per_page = 20 