from rest_framework import serializers
from .models import Shop, ShopCheckout, ShopFeePayment


class ShopSerializer(serializers.ModelSerializer):
    remaining_days = serializers.SerializerMethodField()
    
    class Meta:
        model = Shop
        fields = '__all__'
    
    def get_remaining_days(self, obj):
        return obj.calculate_remaining_days()


class ShopListSerializer(serializers.ModelSerializer):
    remaining_days = serializers.SerializerMethodField()
    
    class Meta:
        model = Shop
        fields = [
            'id', 'shop_number', 'tenant_name', 'phone', 'id_card',
            'area', 'lease_start_date', 'fee_end_date', 'remaining_days',
            'is_active'
        ]
    
    def get_remaining_days(self, obj):
        return obj.calculate_remaining_days()


class ShopExpiredSerializer(serializers.ModelSerializer):
    overdue_days = serializers.SerializerMethodField()
    
    class Meta:
        model = Shop
        fields = [
            'id', 'shop_number', 'tenant_name', 'phone', 'area',
            'fee_end_date', 'overdue_days'
        ]
    
    def get_overdue_days(self, obj):
        return obj.calculate_overdue_days()


class ShopCheckoutSerializer(serializers.ModelSerializer):
    class Meta:
        model = ShopCheckout
        fields = '__all__'


class ShopFeePaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = ShopFeePayment
        fields = '__all__'


class RenewFeeSerializer(serializers.Serializer):
    shop_id = serializers.IntegerField()
    months = serializers.IntegerField(min_value=1) 