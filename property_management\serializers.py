from rest_framework import serializers
from .models import Property, PropertyFeePayment


class PropertySerializer(serializers.ModelSerializer):
    remaining_days = serializers.SerializerMethodField()
    
    class Meta:
        model = Property
        fields = '__all__'
    
    def get_remaining_days(self, obj):
        return obj.calculate_remaining_days()


class PropertyListSerializer(serializers.ModelSerializer):
    remaining_days = serializers.SerializerMethodField()
    
    class Meta:
        model = Property
        fields = [
            'id', 'building_number', 'unit_number', 'room_number', 'area',
            'owner_name', 'has_basement', 'has_parking', 'owner_phone',
            'property_fee', 'fee_end_date', 'remaining_days'
        ]
    
    def get_remaining_days(self, obj):
        return obj.calculate_remaining_days()


class PropertyExpiredSerializer(serializers.ModelSerializer):
    overdue_days = serializers.SerializerMethodField()
    
    class Meta:
        model = Property
        fields = [
            'id', 'building_number', 'unit_number', 'room_number', 'area',
            'owner_name', 'has_basement', 'has_parking', 'owner_phone',
            'property_fee', 'fee_end_date', 'overdue_days'
        ]
    
    def get_overdue_days(self, obj):
        return obj.calculate_overdue_days()


class PropertyFeePaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = PropertyFeePayment
        fields = '__all__'


class RenewFeeSerializer(serializers.Serializer):
    property_id = serializers.IntegerField()
    months = serializers.IntegerField(min_value=1) 