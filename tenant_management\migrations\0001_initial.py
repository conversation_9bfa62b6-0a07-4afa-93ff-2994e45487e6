# Generated by Django 5.2.3 on 2025-06-18 09:08

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PropertyFeePayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('building_number', models.CharField(max_length=10, verbose_name='楼号')),
                ('room_number', models.CharField(max_length=10, verbose_name='房号')),
                ('area', models.DecimalField(decimal_places=2, max_digits=7, verbose_name='平米数')),
                ('tenant_name', models.CharField(max_length=50, verbose_name='租客姓名')),
                ('phone', models.CharField(max_length=20, verbose_name='电话')),
                ('payment_date', models.DateField(verbose_name='交费时间')),
                ('start_date', models.DateField(verbose_name='物业费开始时间')),
                ('end_date', models.DateField(verbose_name='物业费结束时间')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='金额')),
                ('months', models.IntegerField(default=1, verbose_name='缴费月数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '物业费支付',
                'verbose_name_plural': '物业费流水统计',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='TenantCheckout',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('building_number', models.CharField(max_length=10, verbose_name='楼号')),
                ('room_number', models.CharField(max_length=10, verbose_name='房号')),
                ('area', models.DecimalField(decimal_places=2, max_digits=7, verbose_name='平米数')),
                ('tenant_name', models.CharField(max_length=50, verbose_name='租客姓名')),
                ('phone', models.CharField(max_length=20, verbose_name='电话')),
                ('checkout_date', models.DateField(verbose_name='退房日期')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '租客退房',
                'verbose_name_plural': '租客退房列表',
                'ordering': ['-checkout_date'],
            },
        ),
        migrations.CreateModel(
            name='Tenant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('building_number', models.CharField(max_length=10, verbose_name='楼号')),
                ('room_number', models.CharField(max_length=10, verbose_name='房号')),
                ('area', models.DecimalField(decimal_places=2, max_digits=7, verbose_name='平米数')),
                ('tenant_name', models.CharField(max_length=50, verbose_name='租客姓名')),
                ('id_card', models.CharField(max_length=18, verbose_name='身份证号')),
                ('resident_count', models.IntegerField(default=1, verbose_name='租住人数')),
                ('owner_name', models.CharField(max_length=50, verbose_name='房东姓名')),
                ('owner_phone', models.CharField(max_length=20, verbose_name='房东电话')),
                ('move_in_date', models.DateField(verbose_name='入住时间')),
                ('fee_end_date', models.DateField(verbose_name='物业费到期时间')),
                ('property_fee', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='物业费用')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否在住')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '租客',
                'verbose_name_plural': '租客管理',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['building_number', 'room_number'], name='tenant_mana_buildin_9aef99_idx'), models.Index(fields=['tenant_name'], name='tenant_mana_tenant__6adeff_idx'), models.Index(fields=['fee_end_date'], name='tenant_mana_fee_end_604fd5_idx')],
            },
        ),
    ]
